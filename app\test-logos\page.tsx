'use client';

import React, { useState, useEffect } from 'react';
import RoundtripDomesticCard from '@/app/flights/results/components/round-trip-domestic-list/components/RoundtripDomesticCard';
import OneWayCard from '@/app/flights/results/components/one-way-list/components/OneWayCard';
import RoundTripInternationalCard from '@/app/flights/results/components/round-trip-international-list/components/RoundTripInternationalCard';

interface FlightData {
  domestic: any[];
  oneWay: any[];
  international: any[];
}

export default function TestLogosPage() {
  const [flightData, setFlightData] = useState<FlightData>({
    domestic: [],
    oneWay: [],
    international: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadMockData = async () => {
      try {
        // Load domestic round-trip data
        const domesticResponse = await fetch('/assets/json/dummydata/domestic-roundtrip.json');
        const domesticData = await domesticResponse.json();
        
        // Load one-way data
        const oneWayResponse = await fetch('/assets/json/dummydata/one-way-mock.json');
        const oneWayData = await oneWayResponse.json();
        
        // Load international round-trip data
        const internationalResponse = await fetch('/assets/json/dummydata/international-roundtrip-mock.json');
        const internationalData = await internationalResponse.json();

        setFlightData({
          domestic: domesticData.payload.searchResult.tripInfos.RETURN.slice(0, 5), // First 5 entries
          oneWay: oneWayData.payload.searchResult.tripInfos.ONEWAY,
          international: internationalData.payload.searchResult.tripInfos.RETURN
        });
        setLoading(false);
      } catch (error) {
        console.error('Error loading mock data:', error);
        setLoading(false);
      }
    };

    loadMockData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-xl">Loading mock flight data...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Airline Logo Test Page
        </h1>
        
        {/* Domestic Round-Trip Cards */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 border-b-2 border-blue-500 pb-2">
            Domestic Round-Trip Cards
          </h2>
          <div className="space-y-4">
            {flightData.domestic.map((flight, index) => (
              <div key={`domestic-${index}`} className="bg-white rounded-lg shadow-md p-4">
                <RoundtripDomesticCard
                  flight={flight}
                  onSelect={() => console.log('Selected domestic flight:', flight.id)}
                  isSelected={false}
                />
              </div>
            ))}
          </div>
        </section>

        {/* One-Way Cards */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 border-b-2 border-green-500 pb-2">
            One-Way Cards
          </h2>
          <div className="space-y-4">
            {flightData.oneWay.map((flight, index) => (
              <div key={`oneway-${index}`} className="bg-white rounded-lg shadow-md p-4">
                <OneWayCard
                  flight={flight}
                  onSelect={() => console.log('Selected one-way flight:', flight.id)}
                  isSelected={false}
                />
              </div>
            ))}
          </div>
        </section>

        {/* International Round-Trip Cards */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 border-b-2 border-purple-500 pb-2">
            International Round-Trip Cards
          </h2>
          <div className="space-y-4">
            {flightData.international.map((flight, index) => (
              <div key={`international-${index}`} className="bg-white rounded-lg shadow-md p-4">
                <RoundTripInternationalCard
                  flight={flight}
                  onSelect={() => console.log('Selected international flight:', flight.id)}
                  isSelected={false}
                />
              </div>
            ))}
          </div>
        </section>

        {/* Logo Test Grid */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 border-b-2 border-red-500 pb-2">
            Direct Logo Test Grid
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {['6E', 'AI', 'UK', 'SG', 'G8', 'EK', 'QR', 'BA', 'LH', 'AF'].map((code) => (
              <div key={code} className="bg-white rounded-lg shadow-md p-4 text-center">
                <img
                  src={`/AirlineLogo/${code}.png`}
                  alt={code}
                  className="w-16 h-10 object-contain mx-auto mb-2"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/AirlineLogo/All.png';
                  }}
                />
                <p className="text-sm font-medium text-gray-700">{code}</p>
              </div>
            ))}
          </div>
        </section>

        <div className="text-center text-gray-600 mt-8">
          <p>This page tests airline logo display across all flight card components.</p>
          <p>Check the browser console for any image loading errors.</p>
        </div>
      </div>
    </div>
  );
}
