// Utility functions for airline-related operations

// Utility function to get airline logo path based on MAC code
export const getAirlineLogo = (macCode: string, fallbackLogo?: string): string => {
  if (!macCode) {
    return fallbackLogo || '/AirlineLogo/All.png';
  }

  // Extract the airline code from MAC (in case it contains additional info)
  const airlineCode = macCode.split('|')[0] || macCode;

  // Return the path to the airline logo
  return `/AirlineLogo/${airlineCode}.png`;
};

// Utility function to get airline name from MAC code
export const getAirlineName = (macCode: string): string => {
  if (!macCode) return 'Unknown Airline';
  
  // Extract the airline code from MAC (in case it contains additional info)
  const airlineCode = macCode.split('|')[0] || macCode;
  
  // Map of common airline codes to names
  const airlineNames: { [key: string]: string } = {
    '6E': 'IndiGo',
    'AI': 'Air India',
    'SG': 'SpiceJet',
    'UK': 'Vistara',
    'G8': 'GoFirst',
    'IX': 'Air India Express',
    'I5': 'AirAsia India',
    'QR': 'Qatar Airways',
    'EK': 'Emirates',
    'EY': 'Etihad Airways',
    'BA': 'British Airways',
    'LH': 'Lufthansa',
    'AF': 'Air France',
    'KL': 'KLM',
    'TK': 'Turkish Airlines',
    'SQ': 'Singapore Airlines',
    'CX': 'Cathay Pacific',
    'JL': 'Japan Airlines',
    'NH': 'ANA',
    'UA': 'United Airlines',
    'AA': 'American Airlines',
    'DL': 'Delta Air Lines',
    // Add more airline codes as needed
    '9W': 'Jet Airways',
    'S2': 'JetLite',
    'DN': 'Norwegian Air',
    'WF': 'Widerøe',
    'DY': 'Norwegian',
    'FR': 'Ryanair',
    'U2': 'easyJet',
    'VY': 'Vueling',
    'W6': 'Wizz Air',
    'PC': 'Pegasus Airlines',
    'XQ': 'SunExpress',
    'HV': 'Transavia',
    'TP': 'TAP Air Portugal',
    'IB': 'Iberia',
    'AZ': 'ITA Airways',
    'OS': 'Austrian Airlines',
    'LX': 'Swiss International Air Lines',
    'SN': 'Brussels Airlines',
    'SK': 'SAS',
    'AY': 'Finnair',
    'EI': 'Aer Lingus',
    'WY': 'Oman Air',
    'GF': 'Gulf Air',
    'MS': 'EgyptAir',
    'RJ': 'Royal Jordanian',
    'ME': 'Middle East Airlines',
    'SV': 'Saudia',
    'KU': 'Kuwait Airways',
    'QP': 'Akasa Air',
    'MH': 'Malaysia Airlines',
    'TG': 'Thai Airways',
    'CI': 'China Airlines',
    'BR': 'EVA Air',
    'PR': 'Philippine Airlines',
    '5J': 'Cebu Pacific',
    'TR': 'Scoot',
    '3K': 'Jetstar Asia',
    'FD': 'Thai AirAsia',
    'AK': 'AirAsia',
    'OD': 'Batik Air Malaysia',
    'BI': 'Royal Brunei Airlines',
    'PG': 'Bangkok Airways',
    'WE': 'Thai Smile',
    'SL': 'Thai Lion Air',
    'XJ': 'Thai AirAsia X',
    'XT': 'Indonesia AirAsia Extra',
    'QZ': 'Indonesia AirAsia',
    'JT': 'Lion Air',
    'SJ': 'Sriwijaya Air',
    'IN': 'Nam Air',
    'IW': 'Wings Air',
    'GA': 'Garuda Indonesia',
    'ID': 'Batik Air',
    'IL': 'Trigana Air Service',
    'KD': 'Kalstar Aviation',
    'QG': 'Citilink'
  };
  
  return airlineNames[airlineCode] || airlineCode;
};

// Utility function to get airline code from flight number
export const getAirlineCodeFromFlightNumber = (flightNumber: string): string => {
  if (!flightNumber) return '';

  // Extract airline code from flight number (e.g., "6E-234" -> "6E")
  const match = flightNumber.match(/^([A-Z0-9]{2,3})/);
  return match ? match[1] : '';
};

// Utility function to display flight number with MAC code prefix
export const getDisplayFlightNumber = (macCode: string, flightNumber: string): string => {
  if (!flightNumber) return '';
  if (!macCode) return flightNumber;

  // Extract the airline code from MAC (in case it contains additional info)
  const airlineCode = macCode.split('|')[0] || macCode;

  // If flight number already starts with the airline code, return as is
  if (flightNumber.toUpperCase().startsWith(airlineCode.toUpperCase())) {
    return flightNumber;
  }

  // Otherwise, prepend the MAC code with a dash
  return `${airlineCode}-${flightNumber}`;
};
